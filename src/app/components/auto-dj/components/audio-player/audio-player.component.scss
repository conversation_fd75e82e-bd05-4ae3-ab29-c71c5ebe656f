.player {
  height: 100%;
  overflow: hidden;
  transition: 200ms ease-in-out;
  border: 3px solid #006aff;
  border-radius: 1rem;
  z-index: 1;
}

.waveform-container {
  height: 100%;
}

.collapsed {
  height: 0px;
}

.song-details {
  position: absolute;
  top: 0;
  color: white;
  background: #00015b9c;
  padding: 1rem;
  z-index: 10;
}

.loading-spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
