import { Injectable } from '@angular/core';
// @ts-ignore
import Essentia from 'essentia.js/dist/essentia.js-core.es.js';
// @ts-ignore
import { EssentiaWASM } from 'essentia.js/dist/essentia-wasm.web.js';
import { QueueSong, Song } from './auto-di.interfaces';

@Injectable()
export class AutoDjAnalyzerService {
  private essentia: any = null;
  private isInitialized = false;

  private async initializeEssentia(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize the WASM module first
      const wasmModule = await EssentiaWASM();

      // Create Essentia instance with the initialized WASM module
      this.essentia = new Essentia(wasmModule);
      this.isInitialized = true;

      console.log('✅ Essentia.js initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Essentia.js:', error);
      throw new Error('Failed to initialize audio analysis engine');
    }
  }

  public async analyzeSongs(songs: Song[]): Promise<QueueSong[]> {
    // Initialize Essentia.js if not already done
    await this.initializeEssentia();

    const analyzedSongs: QueueSong[] = [];

    // Create a single AudioContext for all decoding operations
    const audioContext = new AudioContext();

    console.log(`Starting analysis for ${songs.length} songs...`);

    for (const song of songs) {
      try {
        // 1. Fetch the audio file from the source URL
        const response = await fetch(song.src);
        const arrayBuffer = await response.arrayBuffer();

        // 2. Decode the audio data into an AudioBuffer
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // 3. Convert the audio buffer to a vector Essentia can process
        const audioVector = this.essentia.arrayToVector(
          audioBuffer.getChannelData(0)
        );

        // 4. Extract BPM
        const rhythm = this.essentia.RhythmExtractor2013(audioVector);
        const bpm = rhythm.bpm;

        // 5. Extract Key
        const keyResult = this.essentia.KeyExtractor(audioVector);
        const key = `${keyResult.key} ${keyResult.scale}`;

        // 6. Add the new PlayerSong object to our results array
        analyzedSongs.push({
          ...song,
          bpm: Math.round(bpm), // Round BPM for a cleaner value
          key: key,
        });

        console.log(`✅ Analysis complete for song ID: ${song.id}`);
      } catch (error) {
        console.error(`❌ Failed to analyze song ID: ${song.id}`, error);
        // Optional: Add a placeholder for failed songs
        analyzedSongs.push({
          ...song,
          bpm: 0,
          key: 'N/A',
        });
      }
    }

    console.log('All songs have been processed.');
    return analyzedSongs;
  }
}
