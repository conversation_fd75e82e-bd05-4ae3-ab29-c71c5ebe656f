import { Injectable } from '@angular/core';
// @ts-ignore
import Essentia from 'essentia.js/dist/essentia.js-core.es.js';
// @ts-ignore
import EssentiaWASM from 'essentia.js/dist/essentia-wasm.web.js';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from './auto-di.interfaces';

@Injectable()
export class AutoDjAnalyzerService {
  private essentia: any = null;
  private isInitialized = false;

  private async initializeEssentia(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Initializing Essentia.js...');
      console.log('EssentiaWASM type:', typeof EssentiaWASM);
      console.log('Essentia type:', typeof Essentia);

      // Initialize the WASM module with proper configuration
      const wasmModule = await EssentiaWASM({
        locateFile: (path: string, scriptDirectory: string) => {
          console.log('🔍 Locating file:', path, 'in directory:', scriptDirectory);
          // For the WASM file, use the correct path
          if (path.endsWith('.wasm')) {
            const wasmPath = 'essentia-wasm.web.wasm';
            console.log('📁 Using WASM path:', wasmPath);
            return wasmPath;
          }
          return scriptDirectory + path;
        }
      });

      console.log('🔄 WASM module loaded:', !!wasmModule);
      console.log('🔄 Creating Essentia instance...');

      // Create Essentia instance with the initialized WASM module
      this.essentia = new Essentia(wasmModule);
      this.isInitialized = true;

      console.log('✅ Essentia.js initialized successfully');
      console.log('✅ Available methods:', Object.getOwnPropertyNames(this.essentia).filter(name => typeof this.essentia[name] === 'function').slice(0, 10));
    } catch (error) {
      console.error('❌ Failed to initialize Essentia.js:', error);
      console.error('❌ Error name:', error instanceof Error ? error.name : 'Unknown');
      console.error('❌ Error message:', error instanceof Error ? error.message : error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw new Error(`Failed to initialize audio analysis engine: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async testInitialization(): Promise<boolean> {
    try {
      await this.initializeEssentia();
      return true;
    } catch (error) {
      console.error('Test initialization failed:', error);
      return false;
    }
  }

  public async analyzeSongs(songs: Song[]): Promise<QueueSong[]> {
    // Initialize Essentia.js if not already done
    await this.initializeEssentia();

    const analyzedSongs: QueueSong[] = [];

    // Create a single AudioContext for all decoding operations
    const audioContext = new AudioContext();

    console.log(`Starting analysis for ${songs.length} songs...`);

    for (const song of songs) {
      try {
        // 1. Fetch the audio file from the source URL
        const response = await fetch(song.src);
        const arrayBuffer = await response.arrayBuffer();

        // 2. Decode the audio data into an AudioBuffer
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // 3. Convert the audio buffer to a vector Essentia can process
        const audioVector = this.essentia.arrayToVector(
          audioBuffer.getChannelData(0)
        );

        // 4. Extract BPM
        const rhythm = this.essentia.RhythmExtractor2013(audioVector);
        const bpm = rhythm.bpm;

        // 5. Extract Key
        const keyResult = this.essentia.KeyExtractor(audioVector);
        const key = `${keyResult.key} ${keyResult.scale}`;

        // 6. Add the new PlayerSong object to our results array
        analyzedSongs.push({
          ...song,
          bpm: Math.round(bpm), // Round BPM for a cleaner value
          key: key,
        });

        console.log(`✅ Analysis complete for song ID: ${song.id}`);
      } catch (error) {
        console.error(`❌ Failed to analyze song ID: ${song.id}`, error);
        // Optional: Add a placeholder for failed songs
        analyzedSongs.push({
          ...song,
          bpm: 0,
          key: 'N/A',
        });
      }
    }

    console.log('All songs have been processed.');
    return analyzedSongs;
  }
}
